-- Supabase Schema for LGU Project App
-- Run this in your Supabase SQL Editor to create the database structure

-- Create custom types/enums
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');
CREATE TYPE personnel_status AS ENUM ('Active', 'Inactive', 'On Leave', 'Suspended');

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    role VARCHAR(50) DEFAULT 'user',
    status user_status DEFAULT 'ACTIVE',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personnel table
CREATE TABLE personnel (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    profile_photo VARCHAR(500),
    department VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    hire_date DATE,
    status personnel_status DEFAULT 'Active',
    biography TEXT,
    spouse_name VARCHAR(255),
    spouse_occupation VARCHAR(255),
    children_count VARCHAR(10),
    emergency_contact VARCHAR(50),
    children_names TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personnel Documents table
CREATE TABLE personnel_documents (
    id BIGSERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL,
    path VARCHAR(500) NOT NULL,
    personnel_id BIGINT NOT NULL REFERENCES personnel(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_personnel_email ON personnel(email);
CREATE INDEX idx_personnel_department ON personnel(department);
CREATE INDEX idx_personnel_status ON personnel(status);
CREATE INDEX idx_personnel_documents_personnel_id ON personnel_documents(personnel_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_personnel_updated_at BEFORE UPDATE ON personnel
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_personnel_documents_updated_at BEFORE UPDATE ON personnel_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE personnel ENABLE ROW LEVEL SECURITY;
ALTER TABLE personnel_documents ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (you can customize these based on your needs)
-- Allow authenticated users to read all records
CREATE POLICY "Allow authenticated users to read users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to read personnel" ON personnel
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to read personnel documents" ON personnel_documents
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow service role to do everything (for admin operations)
CREATE POLICY "Allow service role full access to users" ON users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role full access to personnel" ON personnel
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role full access to personnel documents" ON personnel_documents
    FOR ALL USING (auth.role() = 'service_role');

-- Insert sample data (optional - matches your mock data structure)
INSERT INTO users (email, name, password, phone, address, role, status) VALUES
('<EMAIL>', 'Demo Admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO9G', '+63 ************', 'Ipil, Zamboanga Sibugay', 'admin', 'ACTIVE'),
('<EMAIL>', 'Administrator', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+63 ************', 'Ipil, Zamboanga Sibugay', 'admin', 'ACTIVE');

INSERT INTO personnel (name, email, phone, address, profile_photo, department, position, hire_date, status, biography, spouse_name, spouse_occupation, children_count, emergency_contact, children_names) VALUES
('John Doe', '<EMAIL>', '+63 ************', 'Ipil, Zamboanga Sibugay', '/images/profiles/john-doe.jpg', 'Fisheries Management', 'Senior Fisheries Officer', '2020-01-15', 'Active', 'Experienced fisheries officer with over 10 years in marine resource management.', 'Jane Doe', 'Teacher', '2', '+63 ************', 'Alice Doe, Bob Doe'),
('Maria Santos', '<EMAIL>', '+63 ************', 'Ipil, Zamboanga Sibugay', '/images/profiles/maria-santos.jpg', 'Aquaculture Development', 'Aquaculture Specialist', '2019-03-20', 'Active', 'Specialist in sustainable aquaculture practices and fish farming techniques.', 'Carlos Santos', 'Engineer', '3', '+63 ************', 'Miguel Santos, Ana Santos, Luis Santos');

-- Comments for documentation
COMMENT ON TABLE users IS 'System users with authentication credentials';
COMMENT ON TABLE personnel IS 'Personnel/staff information and profiles';
COMMENT ON TABLE personnel_documents IS 'Documents associated with personnel records';

COMMENT ON COLUMN users.role IS 'User role (admin, user, etc.)';
COMMENT ON COLUMN users.status IS 'Account status (ACTIVE, INACTIVE, SUSPENDED)';
COMMENT ON COLUMN personnel.status IS 'Employment status (Active, Inactive, On Leave, Suspended)';
COMMENT ON COLUMN personnel.hire_date IS 'Date when the personnel was hired';
COMMENT ON COLUMN personnel_documents.size IS 'File size in bytes';
COMMENT ON COLUMN personnel_documents.path IS 'Storage path or URL to the document';

-- Phase 3: Sync Status Management Tables
-- Real-time sync operation tracking
CREATE TABLE sync_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation_type VARCHAR(50) NOT NULL CHECK (operation_type IN ('upload', 'delete', 'update', 'full_sync', 'webhook')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    estimated_completion TIMESTAMP WITH TIME ZONE,
    triggered_by VARCHAR(255),
    source VARCHAR(50) DEFAULT 'manual' CHECK (source IN ('manual', 'webhook', 'api', 'scheduled')),
    operation_data JSONB DEFAULT '{}',
    error_details JSONB DEFAULT '{}',
    performance_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time connection status tracking
CREATE TABLE connection_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id VARCHAR(255) NOT NULL,
    connection_type VARCHAR(50) NOT NULL CHECK (connection_type IN ('websocket', 'sse', 'polling')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('connected', 'disconnected', 'reconnecting', 'error')),
    last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    connection_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    disconnect_reason VARCHAR(255),
    user_agent TEXT,
    ip_address INET,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sync status snapshots for recovery
CREATE TABLE sync_status_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    snapshot_type VARCHAR(50) NOT NULL CHECK (snapshot_type IN ('hourly', 'daily', 'manual', 'error')),
    total_assets INTEGER NOT NULL DEFAULT 0,
    synced_assets INTEGER NOT NULL DEFAULT 0,
    pending_assets INTEGER NOT NULL DEFAULT 0,
    error_assets INTEGER NOT NULL DEFAULT 0,
    active_operations INTEGER NOT NULL DEFAULT 0,
    last_sync_time TIMESTAMP WITH TIME ZONE,
    system_health VARCHAR(20) DEFAULT 'healthy' CHECK (system_health IN ('healthy', 'warning', 'critical')),
    performance_score INTEGER DEFAULT 100 CHECK (performance_score >= 0 AND performance_score <= 100),
    error_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_sync_time_ms INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sync_operations_status ON sync_operations(status);
CREATE INDEX idx_sync_operations_type ON sync_operations(operation_type);
CREATE INDEX idx_sync_operations_created_at ON sync_operations(created_at DESC);
CREATE INDEX idx_connection_status_client ON connection_status(client_id);
CREATE INDEX idx_connection_status_updated ON connection_status(updated_at DESC);
CREATE INDEX idx_sync_snapshots_created ON sync_status_snapshots(created_at DESC);

-- Functions for sync status management
CREATE OR REPLACE FUNCTION update_sync_operation_progress(
    operation_id UUID,
    new_progress INTEGER,
    processed_count INTEGER DEFAULT NULL,
    failed_count INTEGER DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE sync_operations
    SET
        progress = new_progress,
        processed_items = COALESCE(processed_count, processed_items),
        failed_items = COALESCE(failed_count, failed_items),
        updated_at = NOW(),
        estimated_completion = CASE
            WHEN new_progress > 0 AND new_progress < 100 THEN
                NOW() + (EXTRACT(EPOCH FROM (NOW() - start_time)) / new_progress * (100 - new_progress)) * INTERVAL '1 second'
            ELSE estimated_completion
        END
    WHERE id = operation_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION complete_sync_operation(
    operation_id UUID,
    final_status VARCHAR(20),
    error_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE sync_operations
    SET
        status = final_status,
        progress = CASE WHEN final_status = 'completed' THEN 100 ELSE progress END,
        end_time = NOW(),
        updated_at = NOW(),
        error_details = COALESCE(error_details, error_details)
    WHERE id = operation_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION create_sync_status_snapshot(
    snapshot_type_param VARCHAR(50) DEFAULT 'manual'
) RETURNS UUID AS $$
DECLARE
    snapshot_id UUID;
    asset_stats RECORD;
    active_ops INTEGER;
BEGIN
    -- Get current asset statistics
    SELECT
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE sync_status = 'synced') as synced,
        COUNT(*) FILTER (WHERE sync_status = 'pending') as pending,
        COUNT(*) FILTER (WHERE sync_status = 'error') as errors
    INTO asset_stats
    FROM media_assets
    WHERE deleted_at IS NULL;

    -- Get active operations count
    SELECT COUNT(*) INTO active_ops
    FROM sync_operations
    WHERE status IN ('pending', 'in_progress');

    -- Create snapshot
    INSERT INTO sync_status_snapshots (
        snapshot_type,
        total_assets,
        synced_assets,
        pending_assets,
        error_assets,
        active_operations,
        last_sync_time
    ) VALUES (
        snapshot_type_param,
        COALESCE(asset_stats.total, 0),
        COALESCE(asset_stats.synced, 0),
        COALESCE(asset_stats.pending, 0),
        COALESCE(asset_stats.errors, 0),
        active_ops,
        (SELECT MAX(last_synced_at) FROM media_assets WHERE deleted_at IS NULL)
    ) RETURNING id INTO snapshot_id;

    RETURN snapshot_id;
END;
$$ LANGUAGE plpgsql;

-- Triggers for real-time updates
CREATE OR REPLACE FUNCTION notify_sync_status_change() RETURNS TRIGGER AS $$
BEGIN
    -- Notify real-time subscribers of sync operation changes
    PERFORM pg_notify('sync_status_change', json_build_object(
        'operation_id', NEW.id,
        'operation_type', NEW.operation_type,
        'status', NEW.status,
        'progress', NEW.progress,
        'timestamp', NEW.updated_at
    )::text);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER sync_operations_notify_trigger
    AFTER INSERT OR UPDATE ON sync_operations
    FOR EACH ROW EXECUTE FUNCTION notify_sync_status_change();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
