/**
 * Real-time Media Provider - Phase 2 Implementation
 * 
 * Provides real-time Supabase subscriptions for media library updates.
 * Eliminates setTimeout delays with immediate WebSocket-based updates.
 * 
 * Features:
 * - Real-time database subscriptions
 * - Automatic Redux state updates
 * - Connection management and error handling
 * - Optimistic update confirmations
 * 
 * <AUTHOR> Project Team
 * @version 2.0.0
 */

'use client'

import { useEffect, useRef } from 'react'
import { useAppDispatch } from '@/lib/store'
import { createClient } from '@/utils/supabase/client'
import {
  setSubscriptionActive,
  handleRealtimeInsert,
  handleRealtimeUpdate,
  handleRealtimeDelete,
  confirmOptimisticItem,
  setError
} from '@/lib/redux/slices/mediaSlice'
import type { RealtimeChannel } from '@supabase/supabase-js'

interface RealtimeMediaProviderProps {
  children: React.ReactNode
}

export default function RealtimeMediaProvider({ children }: RealtimeMediaProviderProps) {
  const dispatch = useAppDispatch()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const supabase = createClient()

  useEffect(() => {
    console.log('[RealtimeMediaProvider] Initializing real-time subscriptions...')

    // Create real-time channel for media_assets table
    const channel = supabase
      .channel('media_assets_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'media_assets'
        },
        (payload) => {
          console.log('[RealtimeMediaProvider] New media asset inserted:', payload.new)
          
          // Dispatch real-time insert to Redux
          dispatch(handleRealtimeInsert(payload.new as any))
          
          // Check if this confirms an optimistic update
          const newAsset = payload.new as any
          if (newAsset.cloudinary_public_id) {
            // Look for pending optimistic items that match this asset
            // This will be handled by the component that initiated the upload
            console.log('[RealtimeMediaProvider] Asset confirmed via real-time:', newAsset.cloudinary_public_id)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'media_assets'
        },
        (payload) => {
          console.log('[RealtimeMediaProvider] Media asset updated:', payload.new)
          dispatch(handleRealtimeUpdate(payload.new as any))
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'media_assets'
        },
        (payload) => {
          console.log('[RealtimeMediaProvider] Media asset deleted:', payload.old)
          dispatch(handleRealtimeDelete((payload.old as any).id))
        }
      )
      .subscribe((status) => {
        console.log('[RealtimeMediaProvider] Subscription status:', status)
        
        switch (status) {
          case 'SUBSCRIBED':
            console.log('✅ Real-time media subscriptions active')
            dispatch(setSubscriptionActive(true))
            break
          case 'CHANNEL_ERROR':
            console.error('❌ Real-time subscription error')
            dispatch(setSubscriptionActive(false))
            dispatch(setError('Real-time connection failed'))
            break
          case 'TIMED_OUT':
            console.warn('⏰ Real-time subscription timed out')
            dispatch(setSubscriptionActive(false))
            break
          case 'CLOSED':
            console.log('🔌 Real-time subscription closed')
            dispatch(setSubscriptionActive(false))
            break
          default:
            console.log(`[RealtimeMediaProvider] Status: ${status}`)
        }
      })

    channelRef.current = channel

    // Cleanup function
    return () => {
      console.log('[RealtimeMediaProvider] Cleaning up real-time subscriptions...')
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current)
        channelRef.current = null
      }
      dispatch(setSubscriptionActive(false))
    }
  }, [dispatch, supabase])

  // Handle connection recovery
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && channelRef.current) {
        console.log('[RealtimeMediaProvider] Page visible, checking connection...')
        // Supabase automatically handles reconnection
      }
    }

    const handleOnline = () => {
      console.log('[RealtimeMediaProvider] Network online, reconnecting...')
      // Supabase automatically handles reconnection
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('online', handleOnline)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('online', handleOnline)
    }
  }, [])

  return <>{children}</>
}

/**
 * Hook for triggering immediate sync after upload
 */
export const useImmediateSync = () => {
  const dispatch = useAppDispatch()

  const triggerImmediateSync = async (publicId: string, action: 'upload' | 'delete' | 'update' = 'upload') => {
    try {
      console.log(`[useImmediateSync] Triggering immediate sync for ${publicId}`)
      
      const response = await fetch('/api/cloudinary/webhook', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          public_id: publicId,
          action
        })
      })

      if (!response.ok) {
        throw new Error('Failed to trigger immediate sync')
      }

      const result = await response.json()
      console.log('[useImmediateSync] Immediate sync triggered successfully:', result)
      
      return result
    } catch (error) {
      console.error('[useImmediateSync] Failed to trigger immediate sync:', error)
      dispatch(setError('Failed to trigger immediate sync'))
      throw error
    }
  }

  return { triggerImmediateSync }
}

/**
 * Hook for optimistic updates
 */
export const useOptimisticUpdates = () => {
  const dispatch = useAppDispatch()

  const addOptimistic = (tempId: string, asset: any) => {
    dispatch({
      type: 'media/addOptimisticItem',
      payload: { tempId, asset }
    })
  }

  const confirmOptimistic = (tempId: string, confirmedAsset: any) => {
    dispatch(confirmOptimisticItem({ tempId, confirmedAsset }))
  }

  const rollbackOptimistic = (tempId: string) => {
    dispatch({
      type: 'media/rollbackOptimisticItem',
      payload: tempId
    })
  }

  return {
    addOptimistic,
    confirmOptimistic,
    rollbackOptimistic
  }
}

/**
 * Hook for upload progress tracking
 */
export const useUploadProgress = () => {
  const dispatch = useAppDispatch()

  const updateProgress = (tempId: string, progress: any) => {
    dispatch({
      type: 'media/updateUploadProgress',
      payload: { tempId, progress }
    })
  }

  const clearProgress = (tempId: string) => {
    dispatch({
      type: 'media/clearUploadProgress',
      payload: tempId
    })
  }

  return {
    updateProgress,
    clearProgress
  }
}
